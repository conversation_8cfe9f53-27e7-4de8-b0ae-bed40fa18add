<?php
// เรียกใช้ไฟล์ session เพื่อตรวจสอบสถานะการล็อกอิน
session_start();
require_once '../../dbconnect/_dbconnect.php'; // เรียกใช้ไฟล์ dbconnect
require_once '../utils/BookingLogger.php'; // เรียกใช้ไฟล์ BookingLogger

// กำหนดรูปแบบข้อมูลเป็น json
header('Content-Type: application/json');

// ตรวจสอบสถานะการล็อกอิน
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}



// รับค่าจาก POST แบบปลอดภัยและแน่นอน
/*
Array
(
    [customer_name] => Anuwat
    [phone] => 029009000
    [adults] => 8
    [children] => 2
    [infants] => 1
    [guide] => 0
    [foc] => 0
    [tl] => 0
    [voucher] => Voucher0001
    [agent] => 7Day
    [amount] => 8000
    [remark] => Remark
    [sp_note] => SP Note
    [c-useZone] => 3
    [c-tables] => A19,A19-1,A19-2,A19-3
    [c-booking_id] => 250
    [payment_type] => Transfer
    [special_request] => 2
    [use_date] => 2025-07-20
    [table] => A19,A19-1,A19-2,A19-3
)
*/
// ตรวจสอบว่าเป็นการส่งข้อมูลแบบ POST หรือไม่
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}
// ลอง execute
try {
    $pdo = db_connect();
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // --- รับค่าจาก POST แบบปลอดภัยและแน่นอน ---
    $customer_name     = $_POST['customer_name'] ?? '';
    $phone             = $_POST['phone'] ?? '';
    $adults            = intval($_POST['adults'] ?? 0);
    $children          = intval($_POST['children'] ?? 0);
    $infants           = intval($_POST['infants'] ?? 0);
    $guide             = intval($_POST['guide'] ?? 0);
    $foc               = intval($_POST['foc'] ?? 0);
    $tl                = intval($_POST['tl'] ?? 0);
    $voucher           = $_POST['voucher'] ?? '';
    $agent             = $_POST['agent'] ?? '';
    $amount            = floatval($_POST['amount'] ?? 0);
    $remark            = $_POST['remark'] ?? '';
    $sp_note           = $_POST['sp_note'] ?? '';
    $payment_type      = $_POST['payment_type'] ?? 'Transfer';
    $use_zone          = $_POST['c-useZone'] ?? '';
    $table             = $_POST['c-tables'] ?? '';
    $use_date          = $_POST['use_date'] ?? '';
    $booking_id        = $_POST['c-booking_id'] ?? '';
    $special_request   = intval($_POST['special_request'] ?? 0);
    $book_status       = $_POST['book_status'] ?? 'Pending';
    

    // --- ตรวจสอบค่าที่จำเป็น ---
    if (empty($booking_id)) {
        echo json_encode(['success' => false, 'message' => 'Booking ID is required']);
        exit;
    }

    if (empty($customer_name)) {
        echo json_encode(['success' => false, 'message' => 'Customer name is required']);
        exit;
    }

    // --- ตรวจสอบว่ารายการ booking มีจริงหรือไม่ และดึงข้อมูลเดิม ---
    $checkStmt = $pdo->prepare("SELECT * FROM kp_booking WHERE booking_id = ?");
    $checkStmt->execute([$booking_id]);
    $originalBooking = $checkStmt->fetch(PDO::FETCH_ASSOC);

    if (!$originalBooking) {
        echo json_encode(['success' => false, 'message' => 'Booking not found']);
        exit;
    }

    // --- Check role-based edit permissions ---
    $currentUserKey = $_SESSION['userKey'] ?? '';
    $currentUserRole = $_SESSION['role'] ?? 'user';
    $bookingUserKey = $originalBooking['user_key'] ?? '';

    // Start transaction
    $pdo->beginTransaction();

    // --- ตรวจสอบว่ามีการเปลี่ยนแปลง use_date หรือไม่ ---
    if ($use_date !== $originalBooking['use_date']) {

        // check $use_date and split date
        // $use_date_parts = explode('-', $use_date);
        // $use_date = $use_date_parts[2] . '-' . $use_date_parts[1] . '-' . $use_date_parts[0];

        // search orderNo of month of use_date
        $year = date('Y', strtotime($use_date));
        $month = date('m', strtotime($use_date));
        $prefix = "SWD{$year}{$month}";

        // Query to find the highest orderNo this month using prepared statement
        $sql = "SELECT orderNo FROM kp_booking WHERE orderNo LIKE :prefix ORDER BY orderNo DESC LIMIT 1";
        $stmt = $pdo->prepare($sql);
        $searchPattern = $prefix . '%';
        $stmt->bindParam(':prefix', $searchPattern, PDO::PARAM_STR);
        $stmt->execute();

        $num = $stmt->rowCount();
        if ($num == 0) {
            $newOrderNo = $prefix . "0001";
        } else {
            $row = $stmt->fetch();
            $lastOrderNo = $row['orderNo'];
            $lastOrderNo = substr($lastOrderNo, -4);
            $newOrderNo = $prefix . str_pad($lastOrderNo + 1, 4, '0', STR_PAD_LEFT);
        }

        // --- ตรวจสอบว่า use_date นี้มีการจองซ้ำหรือไม่ ---
        $checkDuplicateStmt = $pdo->prepare("SELECT * FROM kp_booking WHERE use_date = ? AND booking_id != ?");
        $checkDuplicateStmt->execute([$use_date, $booking_id]);
        $duplicateBooking = $checkDuplicateStmt->fetch(PDO::FETCH_ASSOC);

        if ($duplicateBooking) {
            echo json_encode(['success' => false, 'message' => 'Duplicate booking found']);
            exit;
        }

        // Note for new booking: moved from original date and order number
        $originalDate = new DateTime($originalBooking['use_date']);
        $newDate = new DateTime($use_date);
        $formattedOriginalDate = $originalDate->format('d-m-Y');
        $formattedNewDate = $newDate->format('d-m-Y');

        $newBookingNote = "Moved from: {$formattedOriginalDate} (Original Order No: {$originalBooking['orderNo']})";
        $newBookingRemark = $originalBooking['remark'] ?? '';
        if (!empty($newBookingRemark)) {
            $newBookingRemark .= ' | ' . $newBookingNote;
        } else {
            $newBookingRemark = $newBookingNote;
        }

        // Clone the booking with new data
        $cloneSql = "INSERT INTO kp_booking (
            orderNo, name, phone, adult, child, infant, guide, inspection, team_leader,
            use_date, use_zone, voucher, agent, remark, special_request, special_request_note, cruise_id, zone_id,
            tables, amount, user_key, payment_status, payment_note, attactfile, pay_type,
            credit_term, book_status, create_date, update_date
        ) VALUES (
            :orderNo, :name, :phone, :adult, :child, :infant, :guide, :inspection, :team_leader,
            :use_date, :use_zone, :voucher, :agent, :remark, :special_request, :special_request_note, :cruise_id, :zone_id,
            :tables, :amount, :user_key, :payment_status, :payment_note, :attactfile, :pay_type,
            :credit_term, :book_status, NOW(), NOW()
        )";
        
        $cloneStmt = $pdo->prepare($cloneSql);
        $cloneResult = $cloneStmt->execute([
            ':orderNo' => $newOrderNo,
            ':name' => $originalBooking['name'],
            ':phone' => $originalBooking['phone'],
            ':adult' => $adults ?? $originalBooking['adult'],
            ':child' => $children ?? $originalBooking['child'],
            ':infant' => $infants ?? $originalBooking['infant'],
            ':guide' => $guide ?? $originalBooking['guide'],
            ':inspection' => $foc ?? $originalBooking['inspection'],
            ':team_leader' => $tl ?? $originalBooking['team_leader'],
            ':use_date' => $use_date,
            ':use_zone' => $use_zone ?? $originalBooking['use_zone'],
            ':voucher' => $voucher ?? $originalBooking['voucher'],
            ':agent' => $agent ?? $originalBooking['agent'],
            ':remark' => $newBookingRemark,
            ':special_request' => $special_request ?? $originalBooking['special_request'],
            ':special_request_note' => $sp_note ?? $originalBooking['special_request_note'],
            ':cruise_id' => $originalBooking['cruise_id'],
            ':zone_id' => $originalBooking['zone_id'],
            ':tables' => $table ?? $originalBooking['tables'],
            ':amount' => $amount ?? $originalBooking['amount'],
            ':user_key' => $originalBooking['user_key'],
            ':payment_status' => $originalBooking['payment_status'] ?? 'WP',
            ':payment_note' => $originalBooking['payment_note'] ?? null,
            ':attactfile' => $originalBooking['attactfile'] ?? null,
            ':pay_type' => $payment_type ?? $originalBooking['pay_type'] ?? 'Transfer',
            ':credit_term' => $credit_term ?? $originalBooking['credit_term'] ?? null,
            ':book_status' => 'Pending'
        ]);

        if (!$cloneResult) {
            $pdo->rollBack();
            throw new Exception('Failed to create new booking');
        }

        // insert to kp_booking_edit_log
        try {
            $logger = new BookingLogger($pdo);
            $logger->logBookingEdit(
                $newBookingId,
                $newOrderNo,
                [],
                [
                    'name' => $originalBooking['name'],
                    'phone' => $originalBooking['phone'],
                    'adult' => $adults ?? $originalBooking['adult'],
                    'child' => $children ?? $originalBooking['child'],
                    'infant' => $infants ?? $originalBooking['infant'],
                    'guide' => $guide ?? $originalBooking['guide'],
                    'inspection' => $foc ?? $originalBooking['inspection'],
                    'team_leader' => $tl ?? $originalBooking['team_leader'],
                    'use_date' => $use_date,
                    'use_zone' => $use_zone ?? $originalBooking['use_zone'],
                    'voucher' => $voucher ?? $originalBooking['voucher'],
                    'agent' => $agent ?? $originalBooking['agent'],
                    'remark' => $newBookingRemark,
                    'special_request' => $special_request ?? $originalBooking['special_request'],
                    'special_request_note' => $sp_note ?? $originalBooking['special_request_note'],
                    'tables' => $table ?? $originalBooking['tables'],
                    'amount' => $amount ?? $originalBooking['amount'],
                    'pay_type' => $payment_type ?? $originalBooking['pay_type'],
                    'credit_term' => $credit_term ?? $originalBooking['credit_term']
                ],
                'INSERT',
                'New booking created from move'
            );
        } catch (Exception $e) {
            error_log("Failed to log new booking creation: " . $e->getMessage());
            // Don't fail the update if logging fails
        }

        $newBookingId = $pdo->lastInsertId();

        // Note for original booking: moved to new date and new order number
        $originalBookingNote = "Moved to: {$formattedNewDate} (New Order No: {$newOrderNo})";
        $originalBookingRemark = $originalBooking['remark'] ?? '';
        if (!empty($originalBookingRemark)) {
            $originalBookingRemark .= ' | ' . $originalBookingNote;
        } else {
            $originalBookingRemark = $originalBookingNote;
        }

        // Update the original booking: set Adults, Children, Infants to 0, clear tables, set amount to 0, set book_status to 'Change', and add note
        $updateOriginalSql = "UPDATE kp_booking SET
            adult = 0,
            child = 0,
            infant = 0,
            tables = '',
            amount = 0,
            book_status = 'Change',
            remark = :remark,
            update_date = NOW()
            WHERE booking_id = :booking_id";
        
        $updateOriginalStmt = $pdo->prepare($updateOriginalSql);
        $updateOriginalResult = $updateOriginalStmt->execute([
            ':booking_id' => $booking_id,
            ':remark' => $originalBookingRemark
        ]);

        if (!$updateOriginalResult) {
            $pdo->rollBack();
            throw new Exception('Failed to update original booking');
        }

        // insert to kp_booking_edit_log
        try {
            $logger = new BookingLogger($pdo);
            $logger->logBookingEdit(
                $booking_id,
                $originalBooking['orderNo'] ?? '',
                [
                    'adult' => $originalBooking['adult'],
                    'child' => $originalBooking['child'],
                    'infant' => $originalBooking['infant'],
                    'tables' => $originalBooking['tables'],
                    'amount' => $originalBooking['amount'],
                    'book_status' => $originalBooking['book_status'],
                    'remark' => $originalBooking['remark']
                ],
                [
                    'adult' => 0,
                    'child' => 0,
                    'infant' => 0,
                    'tables' => '',
                    'amount' => 0,
                    'book_status' => 'Change',
                    'remark' => $originalBookingRemark
                ],
                'UPDATE',
                'Original booking updated with move note'
            );
        } catch (Exception $e) {
            error_log("Failed to log original booking update: " . $e->getMessage());
            // Don't fail the update if logging fails
        }

        // Copy payment reference images to the new booking if they exist
        try {
            $copyReferencesSql = "INSERT INTO kp_payment_references (booking_id, payment_status, reference_image, note, updated_by, created_at)
                                  SELECT :new_booking_id, payment_status, reference_image, note, updated_by, created_at
                                  FROM kp_payment_references
                                  WHERE booking_id = :original_booking_id";

            $copyReferencesStmt = $pdo->prepare($copyReferencesSql);
            $copyReferencesStmt->execute([
                ':new_booking_id' => $newBookingId,
                ':original_booking_id' => $booking_id
            ]);
        } catch (Exception $e) {
            // If payment references table doesn't exist, continue without error
            // error_log("Payment references table not found or error copying: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Failed to copy payment references']);
            exit;
        }

        // Commit the transaction
        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Booking date changed successfully. New booking created with move note and original booking updated with change note.',
            'data' => [
                'original_booking_id' => $booking_id,
                'new_booking_id' => $newBookingId,
                'new_order_no' => $newOrderNo,
                'old_date' => $originalBooking['use_date'],
                'new_date' => $use_date,
                'original_status' => 'Change',
                'new_status' => 'Pending',
                'original_note' => $originalBookingNote,
                'new_note' => $newBookingNote,
                'formatted_old_date' => $formattedOriginalDate,
                'formatted_new_date' => $formattedNewDate
            ]
        ]);

        exit;
    }else{
        // update all data to database
        $updateSql = "UPDATE kp_booking SET
            name = :name,
            phone = :phone,
            adult = :adult,
            child = :child,
            infant = :infant,
            guide = :guide,
            inspection = :inspection,
            team_leader = :team_leader,
            use_date = :use_date,
            use_zone = :use_zone,
            voucher = :voucher,
            agent = :agent,
            remark = :remark,
            special_request = :special_request,
            special_request_note = :special_request_note,
            tables = :tables,
            amount = :amount,
            pay_type = :pay_type,
            credit_term = :credit_term,
            update_date = NOW()
            WHERE booking_id = :booking_id";

        $updateStmt = $pdo->prepare($updateSql);
        $updateResult = $updateStmt->execute([
            ':booking_id' => $booking_id,
            ':name' => $customer_name,
            ':phone' => $phone,
            ':adult' => $adults ?? $originalBooking['adult'],
            ':child' => $children ?? $originalBooking['child'],
            ':infant' => $infants ?? $originalBooking['infant'],
            ':guide' => $guide ?? $originalBooking['guide'],
            ':inspection' => $foc ?? $originalBooking['inspection'],
            ':team_leader' => $tl ?? $originalBooking['team_leader'],
            ':use_date' => $use_date,
            ':use_zone' => $use_zone ?? $originalBooking['use_zone'],   
            ':voucher' => $voucher ?? $originalBooking['voucher'],
            ':agent' => $agent ?? $originalBooking['agent'],
            ':remark' => $remark ?? $originalBooking['remark'],
            ':special_request' => $special_request ?? $originalBooking['special_request'],
            ':special_request_note' => $sp_note ?? $originalBooking['special_request_note'],
            ':tables' => $table ?? $originalBooking['tables'],
            ':amount' => $amount ?? $originalBooking['amount'],
            ':pay_type' => $payment_type ?? $originalBooking['pay_type'],
            ':credit_term' => $credit_term ?? $originalBooking['credit_term']
        ]);

        if (!$updateResult) {
            $pdo->rollBack();
            throw new Exception('Failed to update booking');
        }
        
        // insert to kp_booking_edit_log
        try {
            $logger = new BookingLogger($pdo);
            $logger->logBookingEdit(
                $booking_id,
                $originalBooking['orderNo'] ?? '',
                $originalBooking,
                [
                    'name' => $customer_name,
                    'phone' => $phone,
                    'adult' => $adults ?? $originalBooking['adult'],
                    'child' => $children ?? $originalBooking['child'],
                    'infant' => $infants ?? $originalBooking['infant'],
                    'guide' => $guide ?? $originalBooking['guide'],
                    'inspection' => $foc ?? $originalBooking['inspection'],
                    'team_leader' => $tl ?? $originalBooking['team_leader'],
                    'use_date' => $use_date,
                    'use_zone' => $use_zone ?? $originalBooking['use_zone'],
                    'voucher' => $voucher ?? $originalBooking['voucher'],
                    'agent' => $agent ?? $originalBooking['agent'],
                    'remark' => $remark ?? $originalBooking['remark'],
                    'special_request' => $special_request ?? $originalBooking['special_request'],
                    'special_request_note' => $sp_note ?? $originalBooking['special_request_note'],
                    'tables' => $table ?? $originalBooking['tables'],
                    'amount' => $amount ?? $originalBooking['amount'],
                    'pay_type' => $payment_type ?? $originalBooking['pay_type'],
                    'credit_term' => $credit_term ?? $originalBooking['credit_term']
                ],
                'UPDATE',
                'Booking updated'
            );
        } catch (Exception $e) {
            error_log("Failed to log booking edit: " . $e->getMessage());
            // Don't fail the update if logging fails
        }
        
        // Commit the transaction
        $pdo->commit();
        echo json_encode([
            'success' => true,
            'message' => 'Booking updated successfully',
            'data' => [
                'booking_id' => $booking_id,
                'name' => $customer_name,
                'phone' => $phone,
                'adult' => $adults ?? $originalBooking['adult'],
                'child' => $children ?? $originalBooking['child'],
                'infant' => $infants ?? $originalBooking['infant'],
                'guide' => $guide ?? $originalBooking['guide'],
                'inspection' => $foc ?? $originalBooking['inspection'],
                'team_leader' => $tl ?? $originalBooking['team_leader'],
                'use_date' => $use_date,
                'use_zone' => $use_zone ?? $originalBooking['use_zone'],
                'voucher' => $voucher ?? $originalBooking['voucher'],
                'agent' => $agent ?? $originalBooking['agent'],
                'remark' => $remark ?? $originalBooking['remark'],
                'special_request' => $special_request ?? $originalBooking['special_request'],
                'special_request_note' => $sp_note ?? $originalBooking['special_request_note'],
                'tables' => $table ?? $originalBooking['tables'],
                'amount' => $amount ?? $originalBooking['amount'],
                'pay_type' => $payment_type ?? $originalBooking['pay_type'],
                'credit_term' => $credit_term ?? $originalBooking['credit_term']
            ]
        ]);
    }


} catch (PDOException $e) {
    // Log ช่วย debug
    error_log("Database error in update_booking.php: " . $e->getMessage());
    error_log("POST DATA: " . print_r($_POST, true));
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred',
        'error' => $e->getMessage() // <-- เฉพาะตอนพัฒนา อย่าลืมลบออกก่อนขึ้น production
    ]);
} catch (Exception $e) {
    error_log("General error in update_booking.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while updating the booking',
        'error' => $e->getMessage()
    ]);
}
?>
