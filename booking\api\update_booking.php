<?php
session_start();
require_once '../../dbconnect/_dbconnect.php';
require_once '../utils/BookingLogger.php';

header('Content-Type: application/json');

if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

// date

// Include role check functions
require_once('../../includes/role_check.php');

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $pdo = db_connect();

    // --- รับค่าจาก POST แบบปลอดภัยและแน่นอน ---
    $booking_id        = $_POST['booking_id'] ?? '';
    $bookingid         = intval($_POST['dataid'] ?? 0);
    $customer_name     = $_POST['customer_name'] ?? '';
    $phone             = $_POST['phone'] ?? '';
    $adults            = intval($_POST['adults'] ?? 0);
    $children          = intval($_POST['children'] ?? 0);
    $infants           = intval($_POST['infants'] ?? 0);
    $guide             = intval($_POST['guide'] ?? 0);
    $foc               = intval($_POST['foc'] ?? 0);
    $leader            = intval($_POST['leader'] ?? 0);
    $voucher           = $_POST['voucher'] ?? '';
    $agent             = $_POST['agent'] ?? '';
    $amount            = floatval($_POST['amount'] ?? 0);
    $remark            = $_POST['remark'] ?? '';
    $payment_type      = $_POST['payment_type'] ?? 'Transfer';
    $special_request   = intval($_POST['special_request'] ?? 0);
    $book_status       = $_POST['edit_booking_status'] ?? 'Pending';
    $table             = $_POST['table'] ?? '';
    $use_date          = $_POST['use_date'] ?? '';
    // $use_zone          = $_POST['use_zone'] ?? '';
    $booking_round     = $_POST['booking_round'] ?? 'Sunset';

    // --- ตรวจสอบค่าที่จำเป็น ---
    if (empty($bookingid)) {
        echo json_encode(['success' => false, 'message' => 'Booking ID is required']);
        exit;
    }

    if (empty($customer_name)) {
        echo json_encode(['success' => false, 'message' => 'Customer name is required']);
        exit;
    }

    // --- ตรวจสอบว่ารายการ booking มีจริงหรือไม่ และดึงข้อมูลเดิม ---
    $checkStmt = $pdo->prepare("SELECT * FROM kp_booking WHERE booking_id = ?");
    $checkStmt->execute([$bookingid]);
    $originalBooking = $checkStmt->fetch(PDO::FETCH_ASSOC);

    if (!$originalBooking) {
        echo json_encode(['success' => false, 'message' => 'Booking not found']);
        exit;
    }

    // --- Check role-based edit permissions ---
    $currentUserKey = $_SESSION['userKey'] ?? '';
    $currentUserRole = $_SESSION['role'] ?? 'user';
    $bookingUserKey = $originalBooking['booking_by'] ?? '';
    

    // $hasEditPermission = false;

    // if ($currentUserRole === 'super_admin') {
    //     // Super admin can edit all bookings
    //     $hasEditPermission = true;
    // } elseif ($currentUserRole === 'admin') {
    //     // Admin can edit all bookings
    //     $hasEditPermission = true;
    // } elseif ($currentUserRole === 'user') {
    //     // User can only edit their own bookings
    //     $hasEditPermission = ($bookingUserKey === $currentUserKey);
    // }

    // if (!$hasEditPermission) {
    //     echo json_encode(['success' => false, 'message' => 'You do not have permission to edit this booking<br/> currentUserKey: ' . $currentUserKey . '<br/> bookingUserKey: ' . $bookingUserKey]);
    //     exit;
    // }

    // --- ตรวจสอบว่ามีคอลัมน์ payment_type หรือไม่ ---
    $columnCheckStmt = $pdo->prepare("SHOW COLUMNS FROM kp_booking LIKE 'payment_type'");
    $columnCheckStmt->execute();
    $paymentTypeExists = $columnCheckStmt->fetch() !== false;


    // --- SQL สำหรับอัปเดตข้อมูล ---
    if ($paymentTypeExists) {
        $updateSql = "UPDATE kp_booking SET
                        name = ?,
                        phone = ?,
                        adult = ?,
                        child = ?,
                        infant = ?,
                        guide = ?,
                        inspection = ?,
                        team_leader = ?,
                        voucher = ?,
                        agent = ?,
                        amount = ?,
                        remark = ?,
                        pay_type = ?,
                        special_request = ?,
                        tables = ?,
                        update_date = NOW(),
                        book_status = ?
                      WHERE booking_id = ?";

        $updateParams = [
            $customer_name,
            $phone,
            $adults,
            $children,
            $infants,
            $guide,
            $foc,
            $leader,
            $voucher,
            $agent,
            $amount,
            $remark,
            $payment_type,
            $special_request,
            $table,
            $book_status,
            $bookingid
        ];
    } else {
        $updateSql = "UPDATE kp_booking SET
                        name = ?,
                        phone = ?,
                        adult = ?,
                        child = ?,
                        infant = ?,
                        guide = ?,
                        inspection = ?,
                        team_leader = ?,
                        voucher = ?,
                        agent = ?,
                        amount = ?,
                        pay_type = ?,
                        remark = ?,
                        special_request = ?,
                        tables = ?,
                        update_date = NOW(),
                        book_status = ?
                      WHERE booking_id = ?";

        $updateParams = [
            $customer_name,
            $phone,
            $adults,
            $children,
            $infants,
            $guide,
            $foc,
            $leader,
            $voucher,
            $agent,
            $amount,
            $payment_type,
            $remark,
            $special_request,
            $table,
            $book_status,
            $bookingid
        ];
    }

    // ลอง execute
    $updateStmt = $pdo->prepare($updateSql);
    $result = $updateStmt->execute($updateParams);

    // check $book_status if status is Cancel, clear tables, adult, child, and infant fields
    if ($book_status === 'Cancel') {
        $clearSql = "UPDATE kp_booking SET
                        tables = '',
                        adult = 0,
                        child = 0,
                        infant = 0,
                        amount = 0                        
                      WHERE booking_id = ?";
        $clearStmt = $pdo->prepare($clearSql);
        $clearStmt->execute([$bookingid]);
    }

    if ($result) {
        // --- Log the booking edit ---
        try {
            $logger = new BookingLogger($pdo);

            // Prepare new data for logging
            $newData = [
                'name' => $customer_name,
                'phone' => $phone,
                'adult' => $adults,
                'child' => $children,
                'infant' => $infants,
                'guide' => $guide,
                'inspection' => $foc,
                'team_leader' => $leader,
                'voucher' => $voucher,
                'agent' => $agent,
                'amount' => $amount,
                'remark' => $remark,
                'pay_type' => $payment_type,
                'special_request' => $special_request,
                'book_status' => $book_status
            ];

            $logger->logBookingEdit(
                $bookingid,
                $originalBooking['orderNo'] ?? '',
                $originalBooking,
                $newData,
                'UPDATE',
                'Booking updated via edit modal'
            );

        } catch (Exception $e) {
            error_log("Failed to log booking edit: " . $e->getMessage());
            // Don't fail the update if logging fails
        }

        echo json_encode([
            'success' => true,
            'message' => 'Booking updated successfully',
            'booking_id' => $bookingid
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update booking']);
    }

} catch (PDOException $e) {
    // Log ช่วย debug
    error_log("Database error in update_booking.php: " . $e->getMessage());
    error_log("POST DATA: " . print_r($_POST, true));
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred',
        'error' => $e->getMessage() // <-- เฉพาะตอนพัฒนา อย่าลืมลบออกก่อนขึ้น production
    ]);
} catch (Exception $e) {
    error_log("General error in update_booking.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while updating the booking',
        'error' => $e->getMessage()
    ]);
}
?>
