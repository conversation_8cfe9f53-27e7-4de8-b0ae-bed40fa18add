<?php
// เรียกใช้ไฟล์ session เพื่อตรวจสอบสถานะการล็อกอิน
session_start();
require_once '../../dbconnect/_dbconnect.php'; // เรียกใช้ไฟล์ dbconnect
require_once '../utils/BookingLogger.php'; // เรียกใช้ไฟล์ BookingLogger

// กำหนดรูปแบบข้อมูลเป็น json
header('Content-Type: application/json');

// ตรวจสอบสถานะการล็อกอิน
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}



// รับค่าจาก POST แบบปลอดภัยและแน่นอน
/*
Array
(
    [customer_name] => Anuwat
    [phone] => 029009000
    [adults] => 8
    [children] => 2
    [infants] => 1
    [guide] => 0
    [foc] => 0
    [tl] => 0
    [voucher] => Voucher0001
    [agent] => 7Day
    [amount] => 8000
    [remark] => Remark
    [sp_note] => SP Note
    [c-useZone] => 3
    [c-tables] => A19,A19-1,A19-2,A19-3
    [c-booking_id] => 250
    [payment_type] => Transfer
    [special_request] => 2
    [use_date] => 2025-07-20
    [table] => A19,A19-1,A19-2,A19-3
)
*/
// ตรวจสอบว่าเป็นการส่งข้อมูลแบบ POST หรือไม่
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}
// ลอง execute
try {
    $pdo = db_connect();
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // --- รับค่าจาก POST แบบปลอดภัยและแน่นอน ---
    $customer_name     = $_POST['customer_name'] ?? '';
    $phone             = $_POST['phone'] ?? '';
    $adults            = intval($_POST['adults'] ?? 0);
    $children          = intval($_POST['children'] ?? 0);
    $infants           = intval($_POST['infants'] ?? 0);
    $guide             = intval($_POST['guide'] ?? 0);
    $foc               = intval($_POST['foc'] ?? 0);
    $tl                = intval($_POST['tl'] ?? 0);
    $voucher           = $_POST['voucher'] ?? '';
    $agent             = $_POST['agent'] ?? '';
    $amount            = floatval($_POST['amount'] ?? 0);
    $remark            = $_POST['remark'] ?? '';
    $sp_note           = $_POST['sp_note'] ?? '';
    $payment_type      = $_POST['payment_type'] ?? 'Transfer';
    $use_zone          = $_POST['c-useZone'] ?? '';
    $table             = $_POST['c-tables'] ?? '';
    $use_date          = $_POST['use_date'] ?? '';
    $booking_id        = $_POST['c-booking_id'] ?? '';
    $special_request   = intval($_POST['special_request'] ?? 0);
    $book_status       = $_POST['book_status'] ?? 'Pending';
    

    // --- ตรวจสอบค่าที่จำเป็น ---
    if (empty($booking_id)) {
        echo json_encode(['success' => false, 'message' => 'Booking ID is required']);
        exit;
    }

    if (empty($customer_name)) {
        echo json_encode(['success' => false, 'message' => 'Customer name is required']);
        exit;
    }

    // --- ตรวจสอบว่ารายการ booking มีจริงหรือไม่ และดึงข้อมูลเดิม ---
    $checkStmt = $pdo->prepare("SELECT * FROM kp_booking WHERE booking_id = ?");
    $checkStmt->execute([$booking_id]);
    $originalBooking = $checkStmt->fetch(PDO::FETCH_ASSOC);

    if (!$originalBooking) {
        echo json_encode(['success' => false, 'message' => 'Booking not found']);
        exit;
    }

    // --- Check role-based edit permissions ---
    $currentUserKey = $_SESSION['userKey'] ?? '';
    $currentUserRole = $_SESSION['role'] ?? 'user';
    $bookingUserKey = $originalBooking['booking_by'] ?? '';

    // check $use_date ว่ามีการเปลี่ยนแปลงหรือไม่
    if ($use_date !== $originalBooking['use_date']) {
        // create function clone booking and create new booking

        // Get all data from the original booking
        $checkSql = "SELECT * FROM kp_booking WHERE booking_id = :booking_id";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bindParam(':booking_id', $bookingId, PDO::PARAM_INT);
        $checkStmt->execute();

        $originalBooking = $checkStmt->fetch(PDO::FETCH_ASSOC);
        if (!$originalBooking) {
            throw new Exception('Booking not found');
        }

        // Generate new order number for the cloned booking
        $newOrderNo = 'SWD' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

        // Ensure the new order number is unique
        $checkOrderSql = "SELECT COUNT(*) as count FROM kp_booking WHERE orderNo = :orderNo";
        $checkOrderStmt = $conn->prepare($checkOrderSql);

        do {
            $checkOrderStmt->execute([':orderNo' => $newOrderNo]);
            $orderExists = $checkOrderStmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;

            if ($orderExists) {
                $newOrderNo = 'SWD' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            }
        } while ($orderExists);

        // Start transaction
        $conn->beginTransaction();

        // Prepare notes for both bookings
        $originalDate = new DateTime($originalBooking['use_date']);
        $newDate = new DateTime($newUseDate);
        $formattedOriginalDate = $originalDate->format('d-m-Y');
        $formattedNewDate = $newDate->format('d-m-Y');

        // Note for new booking: moved from original date and order number
        $newBookingNote = "Moved from: {$formattedOriginalDate} (Original Order No: {$originalBooking['orderNo']})";
        $newBookingRemark = $originalBooking['remark'] ?? '';
        if (!empty($newBookingRemark)) {
            $newBookingRemark .= ' | ' . $newBookingNote;
        } else {
            $newBookingRemark = $newBookingNote;
        }

        // Clone the booking with new data
        $cloneSql = "INSERT INTO kp_booking (
            orderNo, name, phone, adult, child, infant, guide, inspection, team_leader,
            use_date, use_zone, voucher, agent, remark, special_request, cruise_id, zone_id,
            tables, amount, user_key, payment_status, payment_note, attactfile, pay_type,
            credit_term, book_status, create_date, update_date
        ) VALUES (
            :orderNo, :name, :phone, :adult, :child, :infant, :guide, :inspection, :team_leader,
            :use_date, :use_zone, :voucher, :agent, :remark, :special_request, :cruise_id, :zone_id,
            :tables, :amount, :user_key, :payment_status, :payment_note, :attactfile, :pay_type,
            :credit_term, :book_status, NOW(), NOW()
        )";

        $cloneStmt = $conn->prepare($cloneSql);
        $cloneResult = $cloneStmt->execute([
            ':orderNo' => $newOrderNo,
            ':name' => $originalBooking['name'],
            ':phone' => $originalBooking['phone'],
            ':adult' => $adults ?? $originalBooking['adult'],
            ':child' => $children ?? $originalBooking['child'],
            ':infant' => $infants ?? $originalBooking['infant'],
            ':guide' => $originalBooking['guide'],
            ':inspection' => $originalBooking['inspection'],
            ':team_leader' => $originalBooking['team_leader'],
            ':use_date' => $newUseDate,
            ':use_zone' => $originalBooking['use_zone'],
            ':voucher' => $originalBooking['voucher'],
            ':agent' => $originalBooking['agent'],
            ':remark' => $newBookingRemark,
            ':special_request' => $originalBooking['special_request'],
            ':cruise_id' => $originalBooking['cruise_id'],
            ':zone_id' => $originalBooking['zone_id'],
            ':tables' => $newTables ?? $originalBooking['tables'],
            ':amount' => $originalBooking['amount'],
            ':user_key' => $originalBooking['user_key'],
            ':payment_status' => $originalBooking['payment_status'] ?? 'WP',
            ':payment_note' => $originalBooking['payment_note'] ?? null,
            ':attactfile' => $originalBooking['attactfile'] ?? null,
            ':pay_type' => $originalBooking['pay_type'] ?? 'Transfer',
            ':credit_term' => $originalBooking['credit_term'] ?? null,
            ':book_status' => 'Pending'
        ]);

        if (!$cloneResult) {
            $conn->rollBack();
            throw new Exception('Failed to create new booking');
        }

        $newBookingId = $conn->lastInsertId();

        // Note for original booking: moved to new date and new order number
        $originalBookingNote = "Moved to: {$formattedNewDate} (New Order No: {$newOrderNo})";
        $originalBookingRemark = $originalBooking['remark'] ?? '';
        if (!empty($originalBookingRemark)) {
            $originalBookingRemark .= ' | ' . $originalBookingNote;
        } else {
            $originalBookingRemark = $originalBookingNote;
        }

        // Update the original booking: set Adults, Children, Infants to 0, clear tables, set amount to 0, set book_status to 'Change', and add note
        $updateOriginalSql = "UPDATE kp_booking SET
            adult = 0,
            child = 0,
            infant = 0,
            tables = '',
            amount = 0,
            book_status = 'Change',
            remark = :remark,
            update_date = NOW()
            WHERE booking_id = :booking_id";

        $updateOriginalStmt = $conn->prepare($updateOriginalSql);
        $updateOriginalResult = $updateOriginalStmt->execute([
            ':booking_id' => $bookingId,
            ':remark' => $originalBookingRemark
        ]);

        if (!$updateOriginalResult) {
            $conn->rollBack();
            throw new Exception('Failed to update original booking');
        }

        // Copy payment reference images to the new booking if they exist
        try {
            $copyReferencesSql = "INSERT INTO kp_payment_references (booking_id, payment_status, reference_image, note, updated_by, created_at)
                                  SELECT :new_booking_id, payment_status, reference_image, note, updated_by, created_at
                                  FROM kp_payment_references
                                  WHERE booking_id = :original_booking_id";

            $copyReferencesStmt = $conn->prepare($copyReferencesSql);
            $copyReferencesStmt->execute([
                ':new_booking_id' => $newBookingId,
                ':original_booking_id' => $bookingId
            ]);
        } catch (Exception $e) {
            // If payment references table doesn't exist, continue without error
            error_log("Payment references table not found or error copying: " . $e->getMessage());
        }

        // Commit the transaction
        $conn->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Booking date changed successfully. New booking created with move note and original booking updated with change note.',
            'data' => [
                'original_booking_id' => $bookingId,
                'new_booking_id' => $newBookingId,
                'new_order_no' => $newOrderNo,
                'old_date' => $originalBooking['use_date'],
                'new_date' => $newUseDate,
                'original_status' => 'Change',
                'new_status' => 'Pending',
                'original_note' => $originalBookingNote,
                'new_note' => $newBookingNote,
                'formatted_old_date' => $formattedOriginalDate,
                'formatted_new_date' => $formattedNewDate
            ]
        ]);

        // echo json_encode(['success' => false, 'message' => 'Cannot change use date']);
        exit;
    }else{
        $use_date = $originalBooking['use_date'];
    }
    

    // $hasEditPermission = false;

    // if ($currentUserRole === 'super_admin') {
    //     // Super admin can edit all bookings
    //     $hasEditPermission = true;
    // } elseif ($currentUserRole === 'admin') {
    //     // Admin can edit all bookings
    //     $hasEditPermission = true;
    // } elseif ($currentUserRole === 'user') {
    //     // User can only edit their own bookings
    //     $hasEditPermission = ($bookingUserKey === $currentUserKey);
    // }

    // if (!$hasEditPermission) {
    //     echo json_encode(['success' => false, 'message' => 'You do not have permission to edit this booking<br/> currentUserKey: ' . $currentUserKey . '<br/> bookingUserKey: ' . $bookingUserKey]);
    //     exit;
    // }

    // --- ตรวจสอบว่ามีคอลัมน์ payment_type หรือไม่ ---
    $columnCheckStmt = $pdo->prepare("SHOW COLUMNS FROM kp_booking LIKE 'payment_type'");
    $columnCheckStmt->execute();
    $paymentTypeExists = $columnCheckStmt->fetch() !== false;


    // --- SQL สำหรับอัปเดตข้อมูล ---
    if ($paymentTypeExists) {
        $updateSql = "UPDATE kp_booking SET
                        name = ?,
                        phone = ?,
                        adult = ?,
                        child = ?,
                        infant = ?,
                        guide = ?,
                        inspection = ?,
                        team_leader = ?,
                        voucher = ?,
                        agent = ?,
                        amount = ?,
                        remark = ?,
                        pay_type = ?,
                        special_request = ?,
                        special_request_note = ?,
                        tables = ?,
                        update_date = NOW()
                      WHERE booking_id = ?";

        $updateParams = [
            $customer_name,
            $phone,
            $adults,
            $children,
            $infants,
            $guide,
            $foc,
            $tl,
            $voucher,
            $agent,
            $amount,
            $remark,
            $payment_type,
            $special_request,
            $sp_note,
            $table,
            $booking_id
        ];
    } else {
        $updateSql = "UPDATE kp_booking SET
                        name = ?,
                        phone = ?,
                        adult = ?,
                        child = ?,
                        infant = ?,
                        guide = ?,
                        inspection = ?,
                        team_leader = ?,
                        zone_id = ?,
                        voucher = ?,
                        agent = ?,
                        amount = ?,
                        pay_type = ?,
                        remark = ?,
                        special_request = ?,
                        special_request_note = ?,
                        tables = ?,
                        update_date = NOW()
                      WHERE booking_id = ?";

        $updateParams = [
            $customer_name,
            $phone,
            $adults,
            $children,
            $infants,
            $guide,
            $foc,
            $tl,
            $use_zone,
            $voucher,
            $agent,
            $amount,
            $payment_type,
            $remark,
            $special_request,
            $sp_note,
            $table,
            $booking_id
        ];
    }

    // ลอง execute
    $updateStmt = $pdo->prepare($updateSql);
    $result = $updateStmt->execute($updateParams);

    // check $book_status if status is Cancel, clear tables, adult, child, and infant fields
    if ($originalBooking['book_status'] === 'Cancel') {
        $clearSql = "UPDATE kp_booking SET
                        tables = '',
                        adult = 0,
                        child = 0,
                        infant = 0,
                        amount = 0                        
                      WHERE booking_id = ?";
        $clearStmt = $pdo->prepare($clearSql);
        $clearStmt->execute([$booking_id]);
    }

    if ($result) {
        // --- Log the booking edit ---
        try {
            $logger = new BookingLogger($pdo);

            // Prepare new data for logging
            $newData = [
                'name' => $customer_name,
                'phone' => $phone,
                'adult' => $adults,
                'child' => $children,
                'infant' => $infants,
                'guide' => $guide,
                'inspection' => $foc,
                'team_leader' => $tl,
                'voucher' => $voucher,
                'agent' => $agent,
                'amount' => $amount,
                'remark' => $remark,
                'pay_type' => $payment_type,
                'special_request' => $special_request,
                'special_request_note' => $sp_note,
                'book_status' => $book_status
            ];

            $logger->logBookingEdit(
                $booking_id,
                $originalBooking['orderNo'] ?? '',
                $originalBooking,
                $newData,
                'UPDATE',
                'Booking updated via edit modal'
            );

        } catch (Exception $e) {
            error_log("Failed to log booking edit: " . $e->getMessage());
            // Don't fail the update if logging fails
        }

        echo json_encode([
            'success' => true,
            'message' => 'Booking updated successfully',
            'booking_id' => $booking_id
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update booking']);
    }

} catch (PDOException $e) {
    // Log ช่วย debug
    error_log("Database error in update_booking.php: " . $e->getMessage());
    error_log("POST DATA: " . print_r($_POST, true));
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred',
        'error' => $e->getMessage() // <-- เฉพาะตอนพัฒนา อย่าลืมลบออกก่อนขึ้น production
    ]);
} catch (Exception $e) {
    error_log("General error in update_booking.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while updating the booking',
        'error' => $e->getMessage()
    ]);
}
?>
